🚀 BitcoinTalk新帖子监控器 - 完整使用说明
================================================================

📋 程序版本说明
----------------------------------------------------------------
本监控器提供了多个版本，您可以根据需要选择：

1. 🖥️ 独立监控器.py - 推荐版本
   - 完整的图形界面
   - 功能最全面
   - 界面美观易用

2. 🖥️ bitcointalk_monitor_win10.py - 标准GUI版本
   - 图形界面版本
   - 功能完整

3. ⌨️ bitcointalk_monitor_simple.py - 命令行版本
   - 纯文本界面
   - 适合高级用户

🚀 快速开始 (推荐)
----------------------------------------------------------------
方法1: 双击 "一键启动.bat"
- 最简单的启动方式
- 自动检查环境
- 启动独立监控器

方法2: 双击 "启动监控器.bat"
- 启动标准GUI版本

方法3: 双击 "简单启动.bat"
- 启动命令行版本

🎯 功能特点
----------------------------------------------------------------
✅ 专门监控近3天内新发布的帖子(不是活跃帖子)
✅ 智能去重，避免重复通知
✅ 可自定义检查间隔
✅ 详细的日志记录
✅ 友好的图形界面
✅ 自动保存已知帖子
✅ 网络连接测试功能

📊 监控效果示例
----------------------------------------------------------------
当发现新帖子时，会显示：

🎉 发现 1 个新发布的帖子:
======================================================================
1. 🆕 [ANN] 新的加密货币项目
   👤 作者: 某个用户
   🕒 发布时间: June 19, 2025, 02:25:00 PM
   🔗 链接: https://bitcointalk.org/index.php?topic=5547200.0
   📅 发现时间: 2025-06-19 14:30:15
----------------------------------------------------------------------

⚙️ 使用步骤
----------------------------------------------------------------
1. 🧪 测试连接
   - 点击"测试连接"按钮
   - 验证能否正常访问BitcoinTalk论坛
   - 确认网络连接正常

2. ⏱️ 设置间隔
   - 在"检查间隔"框中输入分钟数
   - 建议设置10-30分钟
   - 避免设置太短的间隔

3. 🚀 开始监控
   - 点击"开始监控"按钮
   - 程序开始实时监控
   - 在日志区域查看结果

4. ⏹️ 停止监控
   - 点击"停止监控"按钮
   - 或直接关闭程序窗口

📁 生成的文件说明
----------------------------------------------------------------
程序运行时会自动创建以下文件：

- bitcointalk_posts.json - 存储已知帖子ID
- bitcointalk_new_posts.json - GUI版本数据文件
- known_posts.json - 命令行版本数据文件

这些文件用于避免重复通知，如需重置可删除这些文件。

🔧 故障排除
----------------------------------------------------------------
问题1: 提示"未找到Python"
解决方案:
1. 访问 https://www.python.org/downloads/
2. 下载并安装Python 3.7+
3. 安装时勾选"Add Python to PATH"
4. 重启电脑

问题2: 网络连接失败
解决方案:
1. 检查网络连接
2. 确保能访问 bitcointalk.org
3. 检查防火墙设置
4. 尝试使用VPN

问题3: 程序无响应
解决方案:
1. 关闭程序重新启动
2. 降低检查频率
3. 检查系统资源使用情况

问题4: 中文显示乱码
解决方案:
1. 确保使用Windows 10或更高版本
2. 系统语言设置为中文
3. 重启程序

🎯 创建exe文件 (可选)
----------------------------------------------------------------
如果您想创建独立的exe文件：

1. 双击 "快速创建exe.bat"
2. 等待几分钟完成打包
3. 生成的exe文件可以在任何Windows电脑上运行
4. 无需安装Python环境

注意: 创建exe需要安装PyInstaller，脚本会自动处理。

💡 使用建议
----------------------------------------------------------------
1. 首次使用建议先点击"测试连接"
2. 设置合理的检查间隔(10-30分钟)
3. 保持网络连接稳定
4. 定期查看日志了解运行状态
5. 不要频繁重启程序
6. 避免同时运行多个监控程序

⚠️ 重要提醒
----------------------------------------------------------------
1. 请设置合理的检查间隔，避免对BitcoinTalk服务器造成压力
2. 本程序仅用于学习和研究目的
3. 请遵守BitcoinTalk论坛的使用条款
4. 建议在网络稳定的环境下使用

🎉 开始使用
----------------------------------------------------------------
现在您可以：

1. 双击 "一键启动.bat" 开始使用 (推荐)
2. 或选择其他启动方式

祝您使用愉快！🚀

================================================================
技术支持: 如有问题请检查本说明文件或重新运行诊断工具
================================================================
