# BitcoinTalk论坛实时监控系统

这是一个用于监控BitcoinTalk论坛"Announcements (Altcoins)"版块的实时监控工具，可以自动检测近3天内发布的新帖子和更新。

## 功能特点

- 🔍 **实时监控**: 定期检查BitcoinTalk论坛的最新动态
- ⏰ **时间过滤**: 只关注近3天内的帖子和更新
- 🆕 **新帖检测**: 自动识别新发布的帖子
- 🔄 **更新检测**: 监控现有帖子的最新回复
- 💾 **数据持久化**: 保存历史数据，避免重复通知
- 📊 **详细日志**: 完整的监控日志记录

## 文件说明

- `bitcointalk_monitor.py` - 主监控程序
- `run_monitor.py` - 启动脚本，提供命令行界面
- `test_monitor.py` - 测试脚本，验证基础功能
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 单次扫描
```bash
python run_monitor.py --once
```

### 2. 持续监控（默认5分钟间隔）
```bash
python run_monitor.py
```

### 3. 自定义监控间隔
```bash
python run_monitor.py --interval 10  # 10分钟间隔
```

### 4. 测试基础功能
```bash
python test_monitor.py
```

## 输出格式

当发现新帖子或更新时，系统会输出以下格式的信息：

```
🆕 [NEW] 帖子标题
👤 作者: 用户名
🕒 最后回复: June 19, 2025, 07:00:34 AM
🔗 链接: https://bitcointalk.org/index.php?topic=xxxxx
==================================================

🔄 [UPDATED] 另一个帖子标题
👤 作者: 用户名
🕒 最后回复: Today at 06:40:33 AM
🔗 链接: https://bitcointalk.org/index.php?topic=xxxxx
==================================================
```

## 数据文件

- `bitcointalk_data.json` - 存储历史帖子数据
- `bitcointalk_monitor.log` - 监控日志文件
- `debug_content.txt` - 调试用的原始页面内容（测试时生成）

## 监控逻辑

1. **获取页面**: 从BitcoinTalk论坛获取最新的帖子列表
2. **解析内容**: 提取帖子标题、作者、最后回复时间等信息
3. **时间过滤**: 只保留近3天内有活动的帖子
4. **变化检测**: 与之前保存的数据比较，识别新帖子和更新
5. **通知输出**: 格式化显示新发现的内容
6. **数据保存**: 更新本地数据文件

## 注意事项

- 监控间隔建议不要设置得太短（建议5分钟以上），避免对服务器造成压力
- 首次运行时会显示所有近3天内的帖子作为基准
- 程序会自动处理网络错误和页面解析错误
- 使用 Ctrl+C 可以安全停止监控程序

## 故障排除

### 1. 网络连接问题
如果出现网络错误，程序会自动重试。请检查：
- 网络连接是否正常
- 是否能正常访问 bitcointalk.org

### 2. 解析错误
如果页面结构发生变化导致解析失败：
- 运行 `python test_monitor.py` 进行诊断
- 检查生成的 `debug_content.txt` 文件
- 可能需要更新解析逻辑

### 3. 权限问题
确保程序有权限：
- 创建和写入日志文件
- 创建和更新数据文件

## 自定义配置

可以修改 `bitcointalk_monitor.py` 中的以下参数：

- `base_url`: 监控的论坛URL
- `data_file`: 数据文件名
- 时间过滤天数（当前为3天）
- 请求头和超时设置

## 扩展功能

这个监控系统可以轻松扩展：

- 添加邮件或微信通知
- 监控多个论坛版块
- 添加关键词过滤
- 集成到其他系统中
- 添加Web界面

## 技术栈

- Python 3.7+
- requests - HTTP请求
- BeautifulSoup4 - HTML解析
- 标准库：json, datetime, logging等
