# BitcoinTalk新帖子监控器 - Windows 10安装说明

## 🎯 功能介绍

这是一个专为Windows 10设计的BitcoinTalk论坛新帖子监控程序，具有图形界面，可以：

- 🔍 **实时监控**: 自动检测BitcoinTalk论坛近3天内新发布的帖子
- 🖥️ **图形界面**: 友好的Windows GUI界面，操作简单
- ⏰ **自定义间隔**: 可设置检查间隔时间（建议10分钟以上）
- 📊 **详细日志**: 实时显示监控日志和发现的新帖子
- 💾 **智能记忆**: 自动记住已知帖子，避免重复通知

## 📋 系统要求

- Windows 10 或更高版本
- Python 3.7 或更高版本
- 网络连接

## 🚀 安装步骤

### 步骤1: 安装Python

1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新版本的Python（推荐3.9或更高）
3. 运行安装程序时，**务必勾选 "Add Python to PATH"**
4. 完成安装后，重启电脑

### 步骤2: 验证Python安装

1. 按 `Win + R` 打开运行对话框
2. 输入 `cmd` 并按回车
3. 在命令行中输入 `python --version`
4. 如果显示Python版本号，说明安装成功

### 步骤3: 运行监控器

1. 双击 `启动监控器.bat` 文件
2. 程序会自动检查Python环境并启动监控器
3. 如果出现错误，请按照提示操作

## 🖥️ 使用说明

### 界面介绍

程序启动后会显示一个图形界面，包含：

- **标题栏**: 显示程序名称
- **控制面板**: 设置检查间隔和控制按钮
- **状态栏**: 显示当前状态
- **日志区域**: 显示详细的监控日志

### 操作步骤

1. **测试连接**: 点击"🧪 测试连接"按钮验证网络连接
2. **设置间隔**: 在"检查间隔"框中输入分钟数（建议10-30分钟）
3. **开始监控**: 点击"🚀 开始监控"按钮开始实时监控
4. **查看结果**: 在日志区域查看发现的新帖子
5. **停止监控**: 点击"⏹️ 停止监控"按钮停止

### 监控结果示例

当发现新帖子时，会在日志中显示：

```
[14:30:15] 🎉 发现 1 个新发布的帖子:
============================================================
1. 🆕 [ANN] 新的加密货币项目
   👤 作者: 某个用户
   🕒 发布时间: June 19, 2025, 02:25:00 PM
   🔗 链接: https://bitcointalk.org/index.php?topic=5547200.0
   📅 发现时间: 2025-06-19 14:30:15
--------------------------------------------------
```

## ⚙️ 高级设置

### 修改检查间隔

- 建议设置10分钟以上，避免对服务器造成压力
- 间隔太短可能被网站限制访问
- 间隔太长可能错过一些短时间内的新帖子

### 数据文件

程序会在同目录下创建以下文件：

- `bitcointalk_new_posts.json`: 存储已知帖子ID，避免重复通知

## 🔧 故障排除

### 常见问题

**1. 提示"未找到Python"**
- 确保已正确安装Python
- 安装时勾选了"Add Python to PATH"
- 重启电脑后再试

**2. 网络连接失败**
- 检查网络连接
- 确保可以访问 bitcointalk.org
- 检查防火墙设置

**3. 程序无响应**
- 关闭程序重新启动
- 检查网络连接
- 降低检查频率

**4. 中文显示乱码**
- 确保使用Windows 10或更高版本
- 系统语言设置为中文

### 获取帮助

如果遇到其他问题：

1. 查看日志区域的错误信息
2. 尝试点击"测试连接"诊断问题
3. 重启程序或电脑
4. 检查网络和Python环境

## 📝 注意事项

1. **合理使用**: 请设置合理的检查间隔，避免对BitcoinTalk服务器造成压力
2. **网络稳定**: 确保网络连接稳定，避免频繁的连接错误
3. **数据备份**: 程序会自动保存数据，但建议定期备份重要信息
4. **版本更新**: 如果BitcoinTalk网站结构发生变化，可能需要更新程序

## 🎉 开始使用

现在您可以双击 `启动监控器.bat` 开始使用BitcoinTalk新帖子监控器了！

祝您使用愉快！ 🚀
