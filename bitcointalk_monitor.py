#!/usr/bin/env python3
"""
BitcoinTalk论坛实时监控系统
监控近3天内的新帖子和更新
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime, timedelta
import re
import os
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bitcointalk_monitor.log'),
        logging.StreamHandler()
    ]
)

class BitcoinTalkMonitor:
    def __init__(self):
        self.base_url = "https://bitcointalk.org/index.php?board=159.0"
        self.data_file = "bitcointalk_data.json"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.previous_posts = self.load_previous_data()
        
    def load_previous_data(self) -> Dict:
        """加载之前保存的数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"加载数据文件失败: {e}")
        return {}
    
    def save_data(self, data: Dict):
        """保存数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存数据失败: {e}")
    
    def parse_datetime(self, date_str: str) -> Optional[datetime]:
        """解析BitcoinTalk的时间格式"""
        try:
            # 处理 "Today" 和 "Yesterday" 等特殊情况
            if "Today" in date_str:
                time_part = date_str.split("at")[-1].strip()
                today = datetime.now().date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(today, time_obj)

            if "Yesterday" in date_str:
                time_part = date_str.split("at")[-1].strip()
                yesterday = (datetime.now() - timedelta(days=1)).date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(yesterday, time_obj)

            # 处理标准日期格式 "June 18, 2025, 11:34:13 PM"
            date_patterns = [
                r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)',
                r'(\w+ \d{1,2}, \d{4})',
            ]

            for pattern in date_patterns:
                match = re.search(pattern, date_str)
                if match:
                    date_part = match.group(1)
                    try:
                        if "AM" in date_part or "PM" in date_part:
                            return datetime.strptime(date_part, "%B %d, %Y, %I:%M:%S %p")
                        else:
                            return datetime.strptime(date_part, "%B %d, %Y")
                    except ValueError:
                        continue

            logging.warning(f"无法解析日期: {date_str}")
            return None

        except Exception as e:
            logging.error(f"解析日期时出错: {e}, 日期字符串: {date_str}")
            return None
    
    def is_within_last_3_days(self, post_date: datetime) -> bool:
        """检查帖子是否在近3天内"""
        if not post_date:
            return False
        
        three_days_ago = datetime.now() - timedelta(days=3)
        return post_date >= three_days_ago
    
    def fetch_page(self) -> Optional[str]:
        """获取页面内容"""
        try:
            response = self.session.get(self.base_url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logging.error(f"获取页面失败: {e}")
            return None
    
    def parse_posts(self, html_content: str) -> List[Dict]:
        """解析页面中的帖子信息 - 查找近3天内新发布的帖子"""
        posts = []

        try:
            import re

            # BitcoinTalk论坛结构分析：
            # 每个帖子行包含：标题、作者、回复数、浏览数、最后回复时间
            # 我们需要找到帖子的发布时间，而不是最后回复时间

            # 使用更精确的正则表达式来匹配帖子行
            # 在BitcoinTalk中，帖子信息通常在表格行中
            lines = html_content.split('\n')

            current_post = {}
            posts_found = []

            for i, line in enumerate(lines):
                # 查找帖子标题链接
                title_match = re.search(r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+)\.0"[^>]*>([^<]+)</a>', line)
                if title_match:
                    topic_id, raw_title = title_match.groups()

                    # 清理标题
                    title = re.sub(r'<[^>]+>', '', raw_title).strip()
                    title = title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')

                    # 跳过置顶帖子和规则帖子
                    if (len(title) < 10 or
                        any(keyword in title.lower() for keyword in ['spam', 'rule', 'guideline', 'beware', 'giveaway', 'unofficial list'])):
                        continue

                    current_post = {
                        'topic_id': topic_id,
                        'title': title,
                        'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
                    }

                    # 在接下来的几行中查找作者和发布时间
                    search_end = min(len(lines), i + 15)
                    for j in range(i + 1, search_end):
                        search_line = lines[j]

                        # 查找作者信息 - 通常在帖子标题后的某一行
                        if not current_post.get('author'):
                            author_match = re.search(r'<a href="[^"]*action=profile;u=\d+"[^>]*>([^<]+)</a>', search_line)
                            if author_match:
                                current_post['author'] = author_match.group(1).strip()

                        # 查找发布时间 - 在BitcoinTalk中，这通常是帖子行中的第一个时间
                        # 我们要找的是帖子的创建时间，不是最后回复时间
                        if not current_post.get('created_time'):
                            # 查找时间模式，优先查找较早的时间（发布时间）
                            time_patterns = [
                                r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                                r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                                r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
                            ]

                            for pattern in time_patterns:
                                time_match = re.search(pattern, search_line)
                                if time_match:
                                    time_str = time_match.group(1)
                                    parsed_time = self.parse_datetime(time_str)

                                    if parsed_time:
                                        # 检查这是否是发布时间（通常是较早的时间）
                                        # 如果已经有时间了，比较哪个更早
                                        if (not current_post.get('created_time') or
                                            parsed_time < self.parse_datetime(current_post.get('created_time', ''))):
                                            current_post['created_time'] = time_str
                                            current_post['created_date'] = parsed_time
                                    break

                        # 如果找到了基本信息，可以结束搜索
                        if (current_post.get('author') and current_post.get('created_time')):
                            break

                    # 检查是否是近3天内发布的新帖子
                    if (current_post.get('created_date') and
                        self.is_within_last_3_days(current_post['created_date'])):

                        post_data = {
                            'title': current_post['title'],
                            'url': current_post['url'],
                            'topic_id': current_post['topic_id'],
                            'author': current_post.get('author', ''),
                            'created_time': current_post.get('created_time', ''),
                            'created_date': current_post['created_date'].isoformat(),
                            'scraped_at': datetime.now().isoformat()
                        }
                        posts_found.append(post_data)

            # 去重（基于topic_id）
            seen_topics = set()
            for post in posts_found:
                if post['topic_id'] not in seen_topics:
                    seen_topics.add(post['topic_id'])
                    posts.append(post)

            logging.info(f"找到 {len(posts)} 个近3天内新发布的帖子")

        except Exception as e:
            logging.error(f"解析页面时出错: {e}")

        return posts
    
    def find_new_posts(self, current_posts: List[Dict]) -> List[Dict]:
        """找出新发布的帖子（不包括更新）"""
        new_posts = []

        for post in current_posts:
            post_key = post['topic_id']  # 使用topic_id作为唯一标识

            if post_key not in self.previous_posts:
                # 完全新发布的帖子
                post['status'] = 'NEW_POST'
                new_posts.append(post)
                logging.info(f"发现新帖子: {post['title']}")

        return new_posts
    
    def format_post_info(self, post: Dict) -> str:
        """格式化帖子信息用于输出"""
        return f"""
🆕 [新发布帖子] {post['title']}
👤 作者: {post['author']}
🕒 发布时间: {post['created_time']}
🔗 链接: {post['url']}
{'='*60}"""
    
    def monitor_once(self) -> List[Dict]:
        """执行一次监控"""
        logging.info("开始监控BitcoinTalk论坛新发布的帖子...")

        html_content = self.fetch_page()
        if not html_content:
            return []

        current_posts = self.parse_posts(html_content)
        logging.info(f"找到 {len(current_posts)} 个近3天内新发布的帖子")

        new_posts = self.find_new_posts(current_posts)

        if new_posts:
            logging.info(f"发现 {len(new_posts)} 个新发布的帖子")
            for post in new_posts:
                print(self.format_post_info(post))
        else:
            logging.info("没有发现新发布的帖子")

        # 更新数据
        current_data = {post['topic_id']: post for post in current_posts}
        self.previous_posts.update(current_data)
        self.save_data(self.previous_posts)

        return new_posts
    
    def start_monitoring(self, interval_minutes: int = 5):
        """开始持续监控"""
        logging.info(f"开始持续监控，检查间隔: {interval_minutes} 分钟")
        
        while True:
            try:
                self.monitor_once()
                logging.info(f"等待 {interval_minutes} 分钟后进行下次检查...")
                time.sleep(interval_minutes * 60)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控过程中出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

if __name__ == "__main__":
    monitor = BitcoinTalkMonitor()
    
    # 首次运行，获取当前状态
    print("正在进行首次扫描...")
    initial_posts = monitor.monitor_once()
    
    if initial_posts:
        print(f"\n发现 {len(initial_posts)} 个近3天内的帖子:")
        for post in initial_posts:
            print(monitor.format_post_info(post))
    
    # 询问是否开始持续监控
    print("\n是否开始持续监控? (y/n): ", end="")
    if input().lower().startswith('y'):
        monitor.start_monitoring(interval_minutes=5)
