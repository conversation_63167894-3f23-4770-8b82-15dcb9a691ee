#!/usr/bin/env python3
"""
BitcoinTalk论坛实时监控系统
监控近3天内的新帖子和更新
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime, timedelta
import re
import os
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bitcointalk_monitor.log'),
        logging.StreamHandler()
    ]
)

class BitcoinTalkMonitor:
    def __init__(self):
        self.base_url = "https://bitcointalk.org/index.php?board=159.0"
        self.data_file = "bitcointalk_data.json"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.previous_posts = self.load_previous_data()
        
    def load_previous_data(self) -> Dict:
        """加载之前保存的数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"加载数据文件失败: {e}")
        return {}
    
    def save_data(self, data: Dict):
        """保存数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存数据失败: {e}")
    
    def parse_datetime(self, date_str: str) -> Optional[datetime]:
        """解析BitcoinTalk的时间格式"""
        try:
            # 处理 "Today" 和 "Yesterday" 等特殊情况
            if "Today" in date_str:
                time_part = date_str.split("at")[-1].strip()
                today = datetime.now().date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(today, time_obj)
            
            # 处理标准日期格式 "June 18, 2025, 11:34:13 PM"
            date_patterns = [
                r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)',
                r'(\w+ \d{1,2}, \d{4})',
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, date_str)
                if match:
                    date_part = match.group(1)
                    try:
                        if "AM" in date_part or "PM" in date_part:
                            return datetime.strptime(date_part, "%B %d, %Y, %I:%M:%S %p")
                        else:
                            return datetime.strptime(date_part, "%B %d, %Y")
                    except ValueError:
                        continue
            
            logging.warning(f"无法解析日期: {date_str}")
            return None
            
        except Exception as e:
            logging.error(f"解析日期时出错: {e}, 日期字符串: {date_str}")
            return None
    
    def is_within_last_3_days(self, post_date: datetime) -> bool:
        """检查帖子是否在近3天内"""
        if not post_date:
            return False
        
        three_days_ago = datetime.now() - timedelta(days=3)
        return post_date >= three_days_ago
    
    def fetch_page(self) -> Optional[str]:
        """获取页面内容"""
        try:
            response = self.session.get(self.base_url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logging.error(f"获取页面失败: {e}")
            return None
    
    def parse_posts(self, html_content: str) -> List[Dict]:
        """解析页面中的帖子信息 - 针对BitcoinTalk结构优化"""
        posts = []

        try:
            # 使用正则表达式直接提取帖子信息，更可靠
            import re

            # 查找所有帖子链接和相关信息
            # BitcoinTalk的帖子链接格式: https://bitcointalk.org/index.php?topic=数字
            topic_pattern = r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+).*?"[^>]*>(.*?)</a>'
            topic_matches = re.findall(topic_pattern, html_content, re.DOTALL)

            # 查找时间信息
            time_patterns = [
                r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)',
            ]

            # 查找作者信息
            author_pattern = r'by <a href="[^"]*?action=profile;u=\d+"[^>]*>(.*?)</a>'

            # 分割页面内容为行，便于匹配
            lines = html_content.split('\n')

            for topic_id, title in topic_matches:
                try:
                    # 清理标题
                    title = re.sub(r'<[^>]+>', '', title).strip()
                    if not title or len(title) < 5:
                        continue

                    # 跳过置顶帖子（通常包含特定关键词）
                    if any(keyword in title.lower() for keyword in ['spam', 'rule', 'guideline', 'beware', 'giveaway']):
                        continue

                    url = f"https://bitcointalk.org/index.php?topic={topic_id}.0"

                    # 在帖子链接附近查找时间和作者信息
                    topic_line_idx = -1
                    for i, line in enumerate(lines):
                        if f"topic={topic_id}" in line and title[:20] in line:
                            topic_line_idx = i
                            break

                    if topic_line_idx == -1:
                        continue

                    # 在附近的行中查找时间和作者
                    last_post_time = ""
                    last_post_date = None
                    author = ""

                    # 搜索范围：当前行前后10行
                    search_start = max(0, topic_line_idx - 10)
                    search_end = min(len(lines), topic_line_idx + 10)

                    for i in range(search_start, search_end):
                        line = lines[i]

                        # 查找时间信息
                        if not last_post_time:
                            for time_pattern in time_patterns:
                                time_match = re.search(time_pattern, line)
                                if time_match:
                                    last_post_time = time_match.group(1)
                                    last_post_date = self.parse_datetime(last_post_time)
                                    break

                        # 查找作者信息
                        if not author:
                            author_match = re.search(author_pattern, line)
                            if author_match:
                                author = author_match.group(1).strip()

                    # 如果没有找到时间信息，尝试更宽泛的搜索
                    if not last_post_time:
                        # 查找包含"by"的行，通常包含时间信息
                        for i in range(search_start, search_end):
                            line = lines[i]
                            if ' by ' in line and ('AM' in line or 'PM' in line):
                                # 提取时间部分
                                time_part = re.search(r'(\w+.*?\d{1,2}:\d{2}:\d{2} [AP]M)', line)
                                if time_part:
                                    last_post_time = time_part.group(1)
                                    last_post_date = self.parse_datetime(last_post_time)
                                    break

                    # 只保留近3天内的帖子
                    if last_post_date and self.is_within_last_3_days(last_post_date):
                        post_data = {
                            'title': title,
                            'url': url,
                            'topic_id': topic_id,
                            'author': author,
                            'last_post_time': last_post_time,
                            'last_post_date': last_post_date.isoformat() if last_post_date else None,
                            'scraped_at': datetime.now().isoformat()
                        }
                        posts.append(post_data)

                except Exception as e:
                    logging.error(f"解析单个帖子时出错 (topic_id: {topic_id}): {e}")
                    continue

            logging.info(f"成功解析 {len(posts)} 个近3天内的帖子")

        except Exception as e:
            logging.error(f"解析页面时出错: {e}")

        return posts
    
    def find_new_posts(self, current_posts: List[Dict]) -> List[Dict]:
        """找出新的或更新的帖子"""
        new_posts = []
        
        for post in current_posts:
            post_key = post['url']  # 使用URL作为唯一标识
            
            if post_key not in self.previous_posts:
                # 完全新的帖子
                post['status'] = 'NEW'
                new_posts.append(post)
            else:
                # 检查是否有更新
                prev_post = self.previous_posts[post_key]
                if post['last_post_time'] != prev_post.get('last_post_time'):
                    post['status'] = 'UPDATED'
                    new_posts.append(post)
        
        return new_posts
    
    def format_post_info(self, post: Dict) -> str:
        """格式化帖子信息用于输出"""
        status_emoji = "🆕" if post['status'] == 'NEW' else "🔄"
        return f"""
{status_emoji} [{post['status']}] {post['title']}
👤 作者: {post['author']}
🕒 最后回复: {post['last_post_time']}
🔗 链接: https://bitcointalk.org{post['url']}
{'='*50}"""
    
    def monitor_once(self) -> List[Dict]:
        """执行一次监控"""
        logging.info("开始监控BitcoinTalk论坛...")
        
        html_content = self.fetch_page()
        if not html_content:
            return []
        
        current_posts = self.parse_posts(html_content)
        logging.info(f"找到 {len(current_posts)} 个近3天内的帖子")
        
        new_posts = self.find_new_posts(current_posts)
        
        if new_posts:
            logging.info(f"发现 {len(new_posts)} 个新的或更新的帖子")
            for post in new_posts:
                print(self.format_post_info(post))
        else:
            logging.info("没有发现新的帖子或更新")
        
        # 更新数据
        current_data = {post['url']: post for post in current_posts}
        self.previous_posts.update(current_data)
        self.save_data(self.previous_posts)
        
        return new_posts
    
    def start_monitoring(self, interval_minutes: int = 5):
        """开始持续监控"""
        logging.info(f"开始持续监控，检查间隔: {interval_minutes} 分钟")
        
        while True:
            try:
                self.monitor_once()
                logging.info(f"等待 {interval_minutes} 分钟后进行下次检查...")
                time.sleep(interval_minutes * 60)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控过程中出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

if __name__ == "__main__":
    monitor = BitcoinTalkMonitor()
    
    # 首次运行，获取当前状态
    print("正在进行首次扫描...")
    initial_posts = monitor.monitor_once()
    
    if initial_posts:
        print(f"\n发现 {len(initial_posts)} 个近3天内的帖子:")
        for post in initial_posts:
            print(monitor.format_post_info(post))
    
    # 询问是否开始持续监控
    print("\n是否开始持续监控? (y/n): ", end="")
    if input().lower().startswith('y'):
        monitor.start_monitoring(interval_minutes=5)
