#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitcoinTalk新帖子监控器 - 简化命令行版本
适用于Windows 10，无需额外依赖
"""

import urllib.request
import json
import time
import re
import os
from datetime import datetime, timedelta

class SimpleBitcoinTalkMonitor:
    def __init__(self):
        self.data_file = "known_posts.json"
        self.known_posts = self.load_known_posts()
        print("🚀 BitcoinTalk新帖子监控器")
        print("=" * 50)
        
    def load_known_posts(self):
        """加载已知帖子"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                pass
        return set()
    
    def save_known_posts(self):
        """保存已知帖子"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.known_posts), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def parse_datetime(self, date_str):
        """解析时间"""
        try:
            if "Today" in date_str:
                time_part = date_str.split("at")[-1].strip()
                today = datetime.now().date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(today, time_obj)
            
            if "Yesterday" in date_str:
                time_part = date_str.split("at")[-1].strip()
                yesterday = (datetime.now() - timedelta(days=1)).date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(yesterday, time_obj)
            
            date_match = re.search(r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)', date_str)
            if date_match:
                return datetime.strptime(date_match.group(1), "%B %d, %Y, %I:%M:%S %p")
                
        except Exception:
            pass
        return None
    
    def is_within_3_days(self, post_date):
        """检查是否在近3天内"""
        if not post_date:
            return False
        three_days_ago = datetime.now() - timedelta(days=3)
        return post_date >= three_days_ago
    
    def fetch_new_posts(self):
        """获取新帖子"""
        try:
            print("🔍 正在检查BitcoinTalk论坛...")
            
            url = "https://bitcointalk.org/index.php?board=159.0"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req, timeout=30) as response:
                content = response.read().decode('utf-8', errors='ignore')
            
            print(f"✅ 页面获取成功")
            
            # 解析新帖子
            new_posts = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                # 查找帖子链接
                topic_match = re.search(r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+)\.0"[^>]*>([^<]+)</a>', line)
                if not topic_match:
                    continue
                
                topic_id, raw_title = topic_match.groups()
                
                # 清理标题
                title = re.sub(r'<[^>]+>', '', raw_title).strip()
                title = title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                
                # 过滤置顶帖子
                skip_keywords = ['spam', 'rule', 'guideline', 'beware', 'giveaway', 'unofficial list']
                if len(title) < 15 or any(keyword in title.lower() for keyword in skip_keywords):
                    continue
                
                # 查找帖子信息
                post_info = {
                    'topic_id': topic_id,
                    'title': title,
                    'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
                }
                
                # 在附近行中查找作者和时间
                search_start = max(0, i - 5)
                search_end = min(len(lines), i + 15)
                
                for j in range(search_start, search_end):
                    search_line = lines[j]
                    
                    # 查找作者
                    if not post_info.get('author'):
                        author_match = re.search(r'<a href="[^"]*action=profile;u=\d+"[^>]*>([^<]+)</a>', search_line)
                        if author_match:
                            post_info['author'] = author_match.group(1).strip()
                    
                    # 查找时间
                    time_patterns = [
                        r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
                    ]
                    
                    for pattern in time_patterns:
                        time_match = re.search(pattern, search_line)
                        if time_match:
                            time_str = time_match.group(1)
                            parsed_time = self.parse_datetime(time_str)
                            
                            if parsed_time and self.is_within_3_days(parsed_time):
                                post_info['time_str'] = time_str
                                post_info['parsed_time'] = parsed_time
                                break
                    
                    if post_info.get('time_str'):
                        break
                
                # 检查是否是新帖子
                if (post_info.get('parsed_time') and 
                    self.is_within_3_days(post_info['parsed_time']) and
                    topic_id not in self.known_posts):
                    
                    new_posts.append({
                        'topic_id': topic_id,
                        'title': title,
                        'author': post_info.get('author', '未知'),
                        'time_str': post_info.get('time_str', ''),
                        'url': post_info['url'],
                        'found_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
                    # 添加到已知帖子
                    self.known_posts.add(topic_id)
            
            return new_posts
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return []
    
    def display_new_posts(self, posts):
        """显示新帖子"""
        if not posts:
            print("📭 没有发现新发布的帖子")
            return
        
        print(f"\n🎉 发现 {len(posts)} 个新发布的帖子:")
        print("=" * 80)
        
        for i, post in enumerate(posts, 1):
            print(f"\n{i}. 🆕 {post['title']}")
            print(f"   👤 作者: {post['author']}")
            print(f"   🕒 发布时间: {post['time_str']}")
            print(f"   🔗 链接: {post['url']}")
            print(f"   📅 发现时间: {post['found_at']}")
            print("-" * 70)
    
    def run_once(self):
        """运行一次检查"""
        new_posts = self.fetch_new_posts()
        self.display_new_posts(new_posts)
        
        if new_posts:
            self.save_known_posts()
        
        return len(new_posts)
    
    def start_monitoring(self, interval_minutes=10):
        """开始持续监控"""
        print(f"🎯 目标: 监控近3天内新发布的帖子")
        print(f"⏱️  检查间隔: {interval_minutes} 分钟")
        print("=" * 50)
        
        # 首次运行
        print("\n📊 首次扫描...")
        initial_count = self.run_once()
        
        if initial_count == 0:
            print("✅ 当前没有新帖子，开始监控模式...")
        
        print(f"\n🔄 开始持续监控... (按 Ctrl+C 停止)")
        
        try:
            while True:
                time.sleep(interval_minutes * 60)
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查新帖子...")
                new_count = self.run_once()
                
                if new_count == 0:
                    print("✅ 暂无新帖子")
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
        except Exception as e:
            print(f"\n❌ 监控过程中出错: {e}")

def main():
    print("🎯 BitcoinTalk新帖子监控器 - 简化版")
    print("专门监控近3天内新发布的帖子")
    print("=" * 50)
    
    monitor = SimpleBitcoinTalkMonitor()
    
    while True:
        print("\n选择运行模式:")
        print("1. 单次检查")
        print("2. 持续监控 (10分钟间隔)")
        print("3. 持续监控 (自定义间隔)")
        print("4. 退出")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n执行单次检查...")
                monitor.run_once()
                input("\n按回车键继续...")
                
            elif choice == '2':
                monitor.start_monitoring(10)
                break
                
            elif choice == '3':
                try:
                    interval = int(input("请输入检查间隔(分钟): "))
                    if interval < 1:
                        print("❌ 间隔时间必须大于0")
                        continue
                    monitor.start_monitoring(interval)
                    break
                except ValueError:
                    print("❌ 请输入有效的数字")
                    continue
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            break

if __name__ == "__main__":
    main()
