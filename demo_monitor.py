#!/usr/bin/env python3
"""
BitcoinTalk监控演示脚本
展示实时监控功能的简化版本
"""

import requests
import time
import json
from datetime import datetime, timedelta
import re
import os

class SimpleBitcoinTalkMonitor:
    def __init__(self):
        self.url = "https://bitcointalk.org/index.php?board=159.0"
        self.data_file = "demo_data.json"
        self.previous_posts = self.load_data()
        
    def load_data(self):
        """加载之前的数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_data(self, data):
        """保存数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def fetch_recent_posts(self):
        """获取近期帖子"""
        try:
            print("🔍 正在获取最新数据...")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(self.url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 简单解析：查找包含今天或昨天的帖子
            content = response.text
            recent_posts = {}
            
            # 查找帖子链接
            topic_pattern = r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+).*?"[^>]*>(.*?)</a>'
            topics = re.findall(topic_pattern, content, re.DOTALL)
            
            # 查找时间信息
            lines = content.split('\n')
            
            for topic_id, raw_title in topics:
                # 清理标题
                title = re.sub(r'<[^>]+>', '', raw_title).strip()
                if len(title) < 10:  # 过滤太短的标题
                    continue
                
                # 跳过置顶帖子
                if any(word in title.lower() for word in ['spam', 'rule', 'beware', 'giveaway']):
                    continue
                
                # 查找这个帖子的时间信息
                for line in lines:
                    if f"topic={topic_id}" in line:
                        # 在这一行附近查找时间
                        line_idx = lines.index(line)
                        search_range = lines[max(0, line_idx-5):line_idx+5]
                        
                        for search_line in search_range:
                            if 'Today' in search_line or 'June 19, 2025' in search_line or 'June 18, 2025' in search_line:
                                # 提取时间信息
                                time_match = re.search(r'(Today.*?[AP]M|June \d+, 2025.*?[AP]M)', search_line)
                                if time_match:
                                    time_info = time_match.group(1)
                                    recent_posts[topic_id] = {
                                        'title': title,
                                        'topic_id': topic_id,
                                        'time_info': time_info,
                                        'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0",
                                        'found_at': datetime.now().isoformat()
                                    }
                                    break
                        break
            
            print(f"✅ 找到 {len(recent_posts)} 个近期帖子")
            return recent_posts
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return {}
    
    def find_new_posts(self, current_posts):
        """找出新帖子"""
        new_posts = []
        
        for topic_id, post in current_posts.items():
            if topic_id not in self.previous_posts:
                post['status'] = 'NEW'
                new_posts.append(post)
            elif post['time_info'] != self.previous_posts[topic_id].get('time_info'):
                post['status'] = 'UPDATED'
                new_posts.append(post)
        
        return new_posts
    
    def display_posts(self, posts):
        """显示帖子信息"""
        if not posts:
            print("📭 没有发现新的帖子或更新")
            return
        
        print(f"\n🎉 发现 {len(posts)} 个新的或更新的帖子:")
        print("=" * 80)
        
        for i, post in enumerate(posts, 1):
            status_emoji = "🆕" if post['status'] == 'NEW' else "🔄"
            print(f"\n{i}. {status_emoji} [{post['status']}] {post['title']}")
            print(f"   🕒 时间: {post['time_info']}")
            print(f"   🔗 链接: {post['url']}")
            print("-" * 60)
    
    def run_once(self):
        """运行一次检查"""
        current_posts = self.fetch_recent_posts()
        if not current_posts:
            return
        
        new_posts = self.find_new_posts(current_posts)
        self.display_posts(new_posts)
        
        # 更新数据
        self.previous_posts.update(current_posts)
        self.save_data(self.previous_posts)
        
        return len(new_posts)
    
    def start_monitoring(self, interval_minutes=5):
        """开始持续监控"""
        print(f"🚀 开始监控BitcoinTalk论坛")
        print(f"⏱️  检查间隔: {interval_minutes} 分钟")
        print(f"🎯 目标: 近期新帖子和更新")
        print("=" * 50)
        
        # 首次运行
        print("\n📊 首次扫描...")
        self.run_once()
        
        print(f"\n🔄 开始持续监控... (按 Ctrl+C 停止)")
        
        try:
            while True:
                time.sleep(interval_minutes * 60)
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查更新...")
                new_count = self.run_once()
                
                if new_count == 0:
                    print("✅ 暂无新内容")
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
        except Exception as e:
            print(f"\n❌ 监控过程中出错: {e}")

def main():
    print("🎯 BitcoinTalk论坛监控演示")
    print("=" * 40)
    
    monitor = SimpleBitcoinTalkMonitor()
    
    print("选择运行模式:")
    print("1. 单次检查")
    print("2. 持续监控 (5分钟间隔)")
    print("3. 持续监控 (自定义间隔)")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            print("\n执行单次检查...")
            monitor.run_once()
        elif choice == '2':
            monitor.start_monitoring(5)
        elif choice == '3':
            interval = int(input("请输入检查间隔(分钟): "))
            monitor.start_monitoring(interval)
        else:
            print("无效选择，执行单次检查...")
            monitor.run_once()
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
