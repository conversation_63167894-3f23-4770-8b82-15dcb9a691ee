#!/usr/bin/env python3
"""
直接测试BitcoinTalk新帖子监控
"""

import urllib.request
import re
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_new_posts():
    """直接获取并显示新发布的帖子"""
    print("🚀 BitcoinTalk新帖子监控测试")
    print("=" * 50)
    print("🎯 目标: 查找近3天内新发布的帖子")
    print()
    
    try:
        # 获取页面
        print("📡 正在获取BitcoinTalk页面...")
        url = "https://bitcointalk.org/index.php?board=159.0"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        req = urllib.request.Request(url, headers=headers)
        with urllib.request.urlopen(req, timeout=30) as response:
            content = response.read().decode('utf-8', errors='ignore')
        
        print(f"✅ 页面获取成功，大小: {len(content):,} 字符")
        
        # 解析帖子
        print("\n🔍 正在解析帖子信息...")
        
        lines = content.split('\n')
        potential_new_posts = []
        
        # 查找帖子
        for i, line in enumerate(lines):
            # 查找帖子链接
            topic_match = re.search(r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+)\.0"[^>]*>([^<]+)</a>', line)
            if not topic_match:
                continue
            
            topic_id, raw_title = topic_match.groups()
            
            # 清理标题
            title = re.sub(r'<[^>]+>', '', raw_title).strip()
            title = title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
            
            # 过滤置顶帖子
            skip_keywords = ['spam', 'rule', 'guideline', 'beware', 'giveaway', 'unofficial list']
            if len(title) < 15 or any(keyword in title.lower() for keyword in skip_keywords):
                continue
            
            # 在附近查找时间信息
            post_info = {
                'topic_id': topic_id,
                'title': title,
                'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
            }
            
            # 搜索时间和作者
            search_start = max(0, i - 5)
            search_end = min(len(lines), i + 15)
            
            for j in range(search_start, search_end):
                search_line = lines[j]
                
                # 查找作者
                if not post_info.get('author'):
                    author_match = re.search(r'<a href="[^"]*action=profile;u=\d+"[^>]*>([^<]+)</a>', search_line)
                    if author_match:
                        post_info['author'] = author_match.group(1).strip()
                
                # 查找时间
                time_patterns = [
                    r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                    r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                    r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
                ]
                
                for pattern in time_patterns:
                    time_match = re.search(pattern, search_line)
                    if time_match:
                        time_str = time_match.group(1)
                        
                        # 解析时间
                        try:
                            if "Today" in time_str:
                                time_part = time_str.split("at")[-1].strip()
                                today = datetime.now().date()
                                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                                parsed_time = datetime.combine(today, time_obj)
                            elif "Yesterday" in time_str:
                                time_part = time_str.split("at")[-1].strip()
                                yesterday = (datetime.now() - timedelta(days=1)).date()
                                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                                parsed_time = datetime.combine(yesterday, time_obj)
                            else:
                                parsed_time = datetime.strptime(time_str, "%B %d, %Y, %I:%M:%S %p")
                            
                            # 检查是否在近3天内
                            three_days_ago = datetime.now() - timedelta(days=3)
                            if parsed_time >= three_days_ago:
                                post_info['time_str'] = time_str
                                post_info['parsed_time'] = parsed_time
                                break
                        except:
                            continue
                
                if post_info.get('time_str'):
                    break
            
            # 如果找到了近期时间，添加到列表
            if post_info.get('time_str'):
                potential_new_posts.append(post_info)
        
        # 去重并排序
        seen_topics = set()
        unique_posts = []
        for post in potential_new_posts:
            if post['topic_id'] not in seen_topics:
                seen_topics.add(post['topic_id'])
                unique_posts.append(post)
        
        # 按时间排序（最新的在前）
        unique_posts.sort(key=lambda x: x.get('parsed_time', datetime.min), reverse=True)
        
        # 显示结果
        print(f"📊 找到 {len(unique_posts)} 个近3天内的帖子")
        print("\n" + "=" * 80)
        print("近3天内发布的帖子:")
        print("=" * 80)
        
        for i, post in enumerate(unique_posts[:10], 1):  # 显示前10个
            print(f"\n{i}. 🆕 {post['title']}")
            print(f"   👤 作者: {post.get('author', '未知')}")
            print(f"   🕒 时间: {post['time_str']}")
            print(f"   🔗 链接: {post['url']}")
            print("-" * 70)
        
        if len(unique_posts) > 10:
            print(f"\n... 还有 {len(unique_posts) - 10} 个帖子")
        
        print(f"\n✅ 分析完成！")
        print(f"💡 这些就是近3天内新发布的帖子")
        print(f"🔄 实际监控时，系统会跟踪这些帖子ID，只通知真正的新帖子")
        
        return unique_posts
        
    except Exception as e:
        print(f"❌ 获取失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return []

if __name__ == "__main__":
    posts = get_new_posts()
    
    if posts:
        print(f"\n🎉 成功找到 {len(posts)} 个近期帖子！")
        print("监控系统已准备就绪。")
    else:
        print("\n❌ 未找到帖子，请检查网络连接或页面结构。")
