#!/usr/bin/env python3
"""
BitcoinTalk新发布帖子监控器
专门监控近3天内新发布的帖子，而不是活跃帖子
"""

import urllib.request
import urllib.parse
import json
import time
import re
import os
from datetime import datetime, timedelta

class NewPostsMonitor:
    def __init__(self):
        self.url = "https://bitcointalk.org/index.php?board=159.0"
        self.data_file = "new_posts_data.json"
        self.known_posts = self.load_known_posts()
        
    def load_known_posts(self):
        """加载已知的帖子ID"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                pass
        return set()
    
    def save_known_posts(self):
        """保存已知的帖子ID"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.known_posts), f)
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def parse_datetime(self, date_str):
        """解析时间字符串"""
        try:
            if "Today" in date_str:
                time_part = date_str.split("at")[-1].strip()
                today = datetime.now().date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(today, time_obj)
            
            if "Yesterday" in date_str:
                time_part = date_str.split("at")[-1].strip()
                yesterday = (datetime.now() - timedelta(days=1)).date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(yesterday, time_obj)
            
            # 标准日期格式
            date_match = re.search(r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)', date_str)
            if date_match:
                return datetime.strptime(date_match.group(1), "%B %d, %Y, %I:%M:%S %p")
                
        except Exception as e:
            print(f"解析时间失败: {e}")
        return None
    
    def is_within_3_days(self, post_date):
        """检查是否在近3天内"""
        if not post_date:
            return False
        three_days_ago = datetime.now() - timedelta(days=3)
        return post_date >= three_days_ago
    
    def fetch_new_posts(self):
        """获取新发布的帖子"""
        try:
            print("🔍 正在检查BitcoinTalk论坛新发布的帖子...")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            req = urllib.request.Request(self.url, headers=headers)
            with urllib.request.urlopen(req, timeout=30) as response:
                content = response.read().decode('utf-8', errors='ignore')
            
            # 解析帖子
            new_posts = []
            lines = content.split('\n')
            
            # 查找帖子信息
            for i, line in enumerate(lines):
                # 查找帖子链接
                topic_match = re.search(r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+)\.0"[^>]*>([^<]+)</a>', line)
                if not topic_match:
                    continue
                
                topic_id, raw_title = topic_match.groups()
                
                # 清理标题
                title = re.sub(r'<[^>]+>', '', raw_title).strip()
                title = title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                
                # 过滤置顶帖子和规则帖子
                if (len(title) < 15 or 
                    any(keyword in title.lower() for keyword in [
                        'spam', 'rule', 'guideline', 'beware', 'giveaway', 
                        'unofficial list', 'incentivising posting', 'users posting'
                    ])):
                    continue
                
                # 在附近行中查找发布时间和作者
                post_info = {
                    'topic_id': topic_id,
                    'title': title,
                    'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
                }
                
                # 搜索作者和时间信息
                search_start = max(0, i - 5)
                search_end = min(len(lines), i + 15)
                
                for j in range(search_start, search_end):
                    search_line = lines[j]
                    
                    # 查找作者
                    if not post_info.get('author'):
                        author_match = re.search(r'<a href="[^"]*action=profile;u=\d+"[^>]*>([^<]+)</a>', search_line)
                        if author_match:
                            post_info['author'] = author_match.group(1).strip()
                    
                    # 查找时间 - 寻找最早的时间（发布时间）
                    time_patterns = [
                        r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
                    ]
                    
                    for pattern in time_patterns:
                        time_match = re.search(pattern, search_line)
                        if time_match:
                            time_str = time_match.group(1)
                            parsed_time = self.parse_datetime(time_str)
                            
                            if parsed_time and self.is_within_3_days(parsed_time):
                                # 如果还没有时间，或者找到了更早的时间，则更新
                                if (not post_info.get('created_time') or 
                                    parsed_time < self.parse_datetime(post_info.get('created_time', ''))):
                                    post_info['created_time'] = time_str
                                    post_info['created_date'] = parsed_time
                            break
                
                # 只保留近3天内发布的新帖子
                if (post_info.get('created_date') and 
                    self.is_within_3_days(post_info['created_date']) and
                    topic_id not in self.known_posts):
                    
                    new_posts.append({
                        'topic_id': topic_id,
                        'title': title,
                        'author': post_info.get('author', '未知'),
                        'created_time': post_info.get('created_time', ''),
                        'url': post_info['url'],
                        'found_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
                    # 添加到已知帖子列表
                    self.known_posts.add(topic_id)
            
            return new_posts
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return []
    
    def display_new_posts(self, posts):
        """显示新发布的帖子"""
        if not posts:
            print("📭 没有发现新发布的帖子")
            return
        
        print(f"\n🎉 发现 {len(posts)} 个新发布的帖子:")
        print("=" * 80)
        
        for i, post in enumerate(posts, 1):
            print(f"\n{i}. 🆕 {post['title']}")
            print(f"   👤 作者: {post['author']}")
            print(f"   🕒 发布时间: {post['created_time']}")
            print(f"   🔗 链接: {post['url']}")
            print(f"   📅 发现时间: {post['found_at']}")
            print("-" * 70)
    
    def run_once(self):
        """运行一次检查"""
        new_posts = self.fetch_new_posts()
        self.display_new_posts(new_posts)
        
        if new_posts:
            self.save_known_posts()
        
        return len(new_posts)
    
    def start_monitoring(self, interval_minutes=10):
        """开始持续监控"""
        print("🚀 BitcoinTalk新帖子监控器")
        print("=" * 50)
        print(f"🎯 目标: 监控近3天内新发布的帖子")
        print(f"⏱️  检查间隔: {interval_minutes} 分钟")
        print("=" * 50)
        
        # 首次运行
        print("\n📊 首次扫描...")
        initial_count = self.run_once()
        
        if initial_count == 0:
            print("✅ 当前没有新帖子，开始监控模式...")
        
        print(f"\n🔄 开始持续监控... (按 Ctrl+C 停止)")
        
        try:
            while True:
                time.sleep(interval_minutes * 60)
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查新帖子...")
                new_count = self.run_once()
                
                if new_count == 0:
                    print("✅ 暂无新帖子")
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
        except Exception as e:
            print(f"\n❌ 监控过程中出错: {e}")

def main():
    print("🎯 BitcoinTalk新帖子监控器")
    print("专门监控近3天内新发布的帖子")
    print("=" * 50)
    
    monitor = NewPostsMonitor()
    
    print("选择运行模式:")
    print("1. 单次检查")
    print("2. 持续监控 (10分钟间隔)")
    print("3. 持续监控 (自定义间隔)")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            print("\n执行单次检查...")
            monitor.run_once()
        elif choice == '2':
            monitor.start_monitoring(10)
        elif choice == '3':
            interval = int(input("请输入检查间隔(分钟): "))
            monitor.start_monitoring(interval)
        else:
            print("无效选择，执行单次检查...")
            monitor.run_once()
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
