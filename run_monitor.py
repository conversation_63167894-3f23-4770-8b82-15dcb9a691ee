#!/usr/bin/env python3
"""
BitcoinTalk监控器启动脚本
提供简单的命令行界面
"""

import argparse
import sys
from bitcointalk_monitor import BitcoinTalkMonitor

def main():
    parser = argparse.ArgumentParser(description='BitcoinTalk论坛实时监控工具')
    parser.add_argument('--interval', '-i', type=int, default=5, 
                       help='监控间隔时间（分钟），默认5分钟')
    parser.add_argument('--once', action='store_true', 
                       help='只运行一次，不持续监控')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='显示详细日志')
    
    args = parser.parse_args()
    
    print("🚀 BitcoinTalk论坛监控器")
    print("=" * 50)
    print(f"📊 监控目标: 近3天内的新帖子和更新")
    print(f"⏱️  检查间隔: {args.interval} 分钟")
    print("=" * 50)
    
    try:
        monitor = BitcoinTalkMonitor()
        
        if args.once:
            print("执行单次扫描...")
            new_posts = monitor.monitor_once()
            if not new_posts:
                print("✅ 没有发现新的帖子或更新")
        else:
            print("开始持续监控... (按 Ctrl+C 停止)")
            monitor.start_monitoring(interval_minutes=args.interval)
            
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
