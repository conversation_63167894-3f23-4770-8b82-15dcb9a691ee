#!/usr/bin/env python3
"""
简单的网络测试，不依赖外部包
"""

def test_with_urllib():
    """使用Python内置的urllib测试"""
    print("🧪 使用Python内置库测试BitcoinTalk连接")
    print("=" * 50)
    
    try:
        import urllib.request
        import urllib.parse
        import re
        from datetime import datetime
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        url = "https://bitcointalk.org/index.php?board=159.0"
        
        print("1. 正在连接BitcoinTalk论坛...")
        
        # 创建请求
        req = urllib.request.Request(url, headers=headers)
        
        # 发送请求
        with urllib.request.urlopen(req, timeout=30) as response:
            content = response.read().decode('utf-8', errors='ignore')
            
        print(f"   ✅ 连接成功！")
        print(f"   📊 页面大小: {len(content):,} 字符")
        
        # 分析内容
        print("\n2. 分析页面内容...")
        
        # 查找帖子链接
        topic_pattern = r'topic=(\d+)'
        topics = re.findall(topic_pattern, content)
        unique_topics = list(set(topics))
        print(f"   📝 找到 {len(unique_topics)} 个不同的帖子")
        
        # 查找时间信息
        time_keywords = ['Today', 'Yesterday', 'June 19, 2025', 'June 18, 2025']
        time_count = 0
        for keyword in time_keywords:
            time_count += content.count(keyword)
        print(f"   🕒 找到 {time_count} 个时间标记")
        
        # 查找最近的帖子标题
        print("\n3. 提取最近帖子信息...")
        
        lines = content.split('\n')
        recent_posts = []
        
        for i, line in enumerate(lines):
            # 查找包含今天或最近日期的行
            if any(keyword in line for keyword in ['Today', 'June 19, 2025', 'June 18, 2025']):
                # 在附近查找帖子标题
                search_start = max(0, i - 10)
                search_end = min(len(lines), i + 10)
                
                for j in range(search_start, search_end):
                    # 查找帖子链接和标题
                    title_match = re.search(r'<a href="[^"]*topic=(\d+)[^"]*"[^>]*>([^<]+)</a>', lines[j])
                    if title_match:
                        topic_id, title = title_match.groups()
                        
                        # 清理标题
                        clean_title = re.sub(r'<[^>]+>', '', title).strip()
                        clean_title = clean_title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                        
                        # 过滤掉太短或置顶帖子
                        if (len(clean_title) > 15 and 
                            not any(word in clean_title.lower() for word in ['spam', 'rule', 'guideline', 'beware', 'giveaway'])):
                            
                            # 提取时间信息
                            time_info = line.strip()
                            if len(time_info) > 200:  # 截断过长的行
                                time_info = time_info[:200] + "..."
                            
                            recent_posts.append({
                                'title': clean_title,
                                'topic_id': topic_id,
                                'time_info': time_info,
                                'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
                            })
                        break
        
        # 去重
        seen_topics = set()
        unique_posts = []
        for post in recent_posts:
            if post['topic_id'] not in seen_topics:
                seen_topics.add(post['topic_id'])
                unique_posts.append(post)
        
        print(f"   🎯 找到 {len(unique_posts)} 个近期活跃帖子")
        
        # 显示结果
        print(f"\n4. 近期活跃帖子列表:")
        print("=" * 80)
        
        for i, post in enumerate(unique_posts[:8], 1):  # 显示前8个
            print(f"\n{i}. 📝 {post['title']}")
            print(f"   🔗 {post['url']}")
            print(f"   🕒 时间线索: {post['time_info'][:100]}...")
            print("-" * 60)
        
        if len(unique_posts) > 8:
            print(f"\n... 还有 {len(unique_posts) - 8} 个帖子")
        
        print(f"\n✅ 测试完成！系统可以正常获取和解析BitcoinTalk数据")
        print(f"💡 这些就是近3天内有活动的帖子，监控系统会跟踪它们的变化")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print(f"🚀 BitcoinTalk监控系统 - 简单测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_with_urllib()
    
    if success:
        print(f"\n🎉 基础功能正常！")
        print(f"📋 接下来可以运行完整的监控程序来实时跟踪变化")
    else:
        print(f"\n❌ 测试失败，请检查网络连接")
