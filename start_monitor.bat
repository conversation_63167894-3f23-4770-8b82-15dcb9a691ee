@echo off
chcp 65001 >nul
title BitcoinTalk论坛监控器

echo.
echo ========================================
echo    BitcoinTalk论坛实时监控系统
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 安装依赖
echo 📦 正在安装依赖包...
pip install requests beautifulsoup4 lxml >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 依赖安装可能失败，但会尝试继续运行
) else (
    echo ✅ 依赖包安装完成
)
echo.

REM 显示菜单
:menu
echo 请选择运行模式:
echo 1. 演示版监控 (推荐新手)
echo 2. 完整版监控
echo 3. 单次测试
echo 4. 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动演示版监控...
    python demo_monitor.py
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 🚀 启动完整版监控...
    python run_monitor.py
    goto end
)

if "%choice%"=="3" (
    echo.
    echo 🧪 运行测试...
    python test_monitor.py
    goto end
)

if "%choice%"=="4" (
    goto end
)

echo 无效选择，请重新输入
echo.
goto menu

:end
echo.
echo 按任意键退出...
pause >nul
