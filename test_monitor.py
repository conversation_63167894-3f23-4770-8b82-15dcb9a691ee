#!/usr/bin/env python3
"""
简化版BitcoinTalk监控测试脚本
"""

import requests
from datetime import datetime, timedelta
import re
import json

def test_fetch_and_parse():
    """测试获取和解析功能"""
    print("🧪 测试BitcoinTalk监控功能...")
    
    url = "https://bitcointalk.org/index.php?board=159.0"
    
    try:
        # 获取页面
        print("📡 正在获取页面...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ 页面获取成功，大小: {len(response.text)} 字符")
        
        # 简单解析最新帖子
        content = response.text
        
        # 查找包含"Today"或最近日期的行
        lines = content.split('\n')
        recent_posts = []
        
        for i, line in enumerate(lines):
            if 'Today' in line or 'June 19, 2025' in line or 'June 18, 2025' in line:
                # 尝试找到相关的帖子标题
                for j in range(max(0, i-10), min(len(lines), i+10)):
                    if '<a href="https://bitcointalk.org/index.php?topic=' in lines[j]:
                        title_match = re.search(r'>(.*?)</a>', lines[j])
                        if title_match:
                            title = title_match.group(1).strip()
                            if title and len(title) > 5:  # 过滤掉太短的标题
                                recent_posts.append({
                                    'title': title,
                                    'time_info': line.strip(),
                                    'line_num': i
                                })
                                break
        
        print(f"\n📊 找到 {len(recent_posts)} 个可能的近期帖子:")
        print("=" * 60)
        
        for idx, post in enumerate(recent_posts[:10], 1):  # 只显示前10个
            print(f"{idx}. {post['title']}")
            print(f"   时间信息: {post['time_info'][:100]}...")
            print("-" * 40)
        
        # 保存原始数据用于调试
        with open('debug_content.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\n💾 原始页面内容已保存到 debug_content.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_fetch_and_parse()
    if success:
        print("\n✅ 基础功能测试通过！")
    else:
        print("\n❌ 测试失败，请检查网络连接或页面结构变化")
