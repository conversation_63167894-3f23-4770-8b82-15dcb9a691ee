@echo off
chcp 65001 >nul
title BitcoinTalk监控器 - 一键启动

echo.
echo ==========================================
echo    BitcoinTalk新帖子监控器 - 一键启动
echo ==========================================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装Python
    echo 3. 安装时勾选"Add Python to PATH"
    echo 4. 重启电脑后再运行此程序
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查必要文件
if not exist "独立监控器.py" (
    echo ❌ 缺少程序文件: 独立监控器.py
    pause
    exit /b 1
)

echo ✅ 程序文件检查通过
echo.

echo 🚀 启动BitcoinTalk新帖子监控器...
echo.
echo 💡 提示:
echo - 程序启动后会显示图形界面
echo - 首次使用建议先点击"测试连接"
echo - 设置合理的检查间隔(建议10分钟以上)
echo - 关闭此窗口不会影响监控程序运行
echo.

REM 启动程序
python 独立监控器.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 以管理员身份运行
    echo 3. 重新安装Python
    echo 4. 检查防火墙设置
    echo.
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)

echo.
pause
