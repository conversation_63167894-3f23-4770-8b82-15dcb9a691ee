🚀 BitcoinTalk新帖子监控器 - Windows 10使用指南
================================================================

📋 文件说明
----------------------------------------------------------------
1. bitcointalk_monitor_win10.py    - 图形界面版本（推荐）
2. bitcointalk_monitor_simple.py   - 命令行版本（备选）
3. 启动监控器.bat                  - 启动图形界面版本
4. 简单启动.bat                    - 启动命令行版本
5. Windows安装说明.md              - 详细安装说明
6. 使用指南.txt                    - 本文件

🎯 功能特点
----------------------------------------------------------------
✅ 监控近3天内新发布的帖子（不是活跃帖子）
✅ 自动去重，避免重复通知
✅ 可自定义检查间隔
✅ 详细的日志记录
✅ 友好的用户界面

🚀 快速开始
----------------------------------------------------------------
方法1：图形界面版本（推荐新手）
1. 双击"启动监控器.bat"
2. 等待程序启动
3. 点击"测试连接"验证网络
4. 设置检查间隔（建议10-30分钟）
5. 点击"开始监控"

方法2：命令行版本（适合高级用户）
1. 双击"简单启动.bat"
2. 选择运行模式
3. 按提示操作

📊 监控结果示例
----------------------------------------------------------------
当发现新帖子时，会显示：

🎉 发现 1 个新发布的帖子:
================================================================================
1. 🆕 [ANN] 新的加密货币项目
   👤 作者: 某个用户
   🕒 发布时间: June 19, 2025, 02:25:00 PM
   🔗 链接: https://bitcointalk.org/index.php?topic=5547200.0
   📅 发现时间: 2025-06-19 14:30:15
----------------------------------------------------------------------

⚙️ 重要设置
----------------------------------------------------------------
1. 检查间隔：建议设置10分钟以上
   - 太短可能被网站限制
   - 太长可能错过新帖子

2. 网络要求：
   - 能够访问 bitcointalk.org
   - 稳定的网络连接

3. 系统要求：
   - Windows 10 或更高版本
   - Python 3.7 或更高版本

🔧 故障排除
----------------------------------------------------------------
问题1：提示"未找到Python"
解决：
1. 访问 https://www.python.org/downloads/
2. 下载并安装Python
3. 安装时勾选"Add Python to PATH"
4. 重启电脑

问题2：网络连接失败
解决：
1. 检查网络连接
2. 确保能访问 bitcointalk.org
3. 检查防火墙设置
4. 尝试使用VPN

问题3：程序无响应
解决：
1. 关闭程序重新启动
2. 降低检查频率
3. 检查系统资源

问题4：中文显示乱码
解决：
1. 确保使用Windows 10
2. 系统语言设置为中文
3. 重启程序

📝 使用建议
----------------------------------------------------------------
1. 首次使用建议先点击"测试连接"
2. 设置合理的检查间隔（10-30分钟）
3. 保持网络连接稳定
4. 定期查看日志了解运行状态
5. 不要频繁重启程序

💾 数据文件
----------------------------------------------------------------
程序会自动创建以下文件：
- bitcointalk_new_posts.json  (图形版数据)
- known_posts.json           (命令行版数据)

这些文件用于记住已知帖子，避免重复通知。
如需重置，可删除这些文件。

🎉 开始使用
----------------------------------------------------------------
现在您可以：
1. 双击"启动监控器.bat"开始使用图形版本
2. 或双击"简单启动.bat"使用命令行版本

祝您使用愉快！🚀

================================================================
技术支持：如有问题请检查Windows安装说明.md文件
================================================================
