@echo off
chcp 65001 >nul
title 创建BitcoinTalk监控器exe文件

echo.
echo ========================================
echo    创建BitcoinTalk监控器exe文件
echo ========================================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 📦 安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    pause
    exit /b 1
)

echo ✅ PyInstaller安装成功
echo.

echo 🔨 创建GUI版本exe文件...
pyinstaller --onefile --windowed --name="BitcoinTalk监控器" --hidden-import=tkinter --hidden-import=urllib.request --hidden-import=threading bitcointalk_monitor_win10.py

if errorlevel 1 (
    echo ❌ GUI版本创建失败
) else (
    echo ✅ GUI版本创建成功
)

echo.
echo 🔨 创建命令行版本exe文件...
pyinstaller --onefile --console --name="BitcoinTalk监控器-命令行版" bitcointalk_monitor_simple.py

if errorlevel 1 (
    echo ❌ 命令行版本创建失败
) else (
    echo ✅ 命令行版本创建成功
)

echo.
echo ========================================
echo 🎉 exe文件创建完成！
echo ========================================
echo.

if exist "dist\BitcoinTalk监控器.exe" (
    echo ✅ GUI版本: dist\BitcoinTalk监控器.exe
)

if exist "dist\BitcoinTalk监控器-命令行版.exe" (
    echo ✅ 命令行版本: dist\BitcoinTalk监控器-命令行版.exe
)

echo.
echo 💡 使用说明:
echo 1. exe文件位于 dist 文件夹中
echo 2. 可以将exe文件复制到任意位置使用
echo 3. 无需安装Python环境
echo 4. 双击exe文件即可运行
echo.

REM 打开dist文件夹
if exist "dist" (
    echo 📁 正在打开dist文件夹...
    explorer dist
)

echo.
pause
