#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建BitcoinTalk监控器的exe文件
"""

import os
import sys
import subprocess

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def create_exe():
    """创建exe文件"""
    print("🔨 正在创建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口（GUI程序）
        "--name=BitcoinTalk监控器",      # exe文件名
        "--icon=NONE",                  # 暂时不使用图标
        "--add-data=*.json;.",          # 包含json配置文件
        "--hidden-import=tkinter",      # 确保包含tkinter
        "--hidden-import=urllib.request",
        "--hidden-import=threading",
        "bitcointalk_monitor_win10.py"  # 主程序文件
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ exe文件创建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ exe文件创建失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到pyinstaller命令，请确保已正确安装")
        return False

def create_simple_exe():
    """创建简化版exe文件"""
    print("🔨 正在创建简化版exe文件...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console",                    # 保留控制台窗口
        "--name=BitcoinTalk监控器-命令行版",
        "bitcointalk_monitor_simple.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ 简化版exe文件创建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 简化版exe文件创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 BitcoinTalk监控器 - exe打包工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "bitcointalk_monitor_win10.py",
        "bitcointalk_monitor_simple.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    print("\n" + "=" * 50)
    print("选择要创建的exe版本:")
    print("1. GUI版本 (推荐)")
    print("2. 命令行版本")
    print("3. 两个都创建")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    success = False
    
    if choice == "1":
        success = create_exe()
    elif choice == "2":
        success = create_simple_exe()
    elif choice == "3":
        success1 = create_exe()
        success2 = create_simple_exe()
        success = success1 or success2
    else:
        print("❌ 无效选择")
        return False
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 exe文件创建完成！")
        print("\n📁 生成的文件位置:")
        print("   - dist/BitcoinTalk监控器.exe (GUI版本)")
        print("   - dist/BitcoinTalk监控器-命令行版.exe (命令行版本)")
        print("\n💡 使用说明:")
        print("   1. 将exe文件复制到任意位置")
        print("   2. 双击exe文件即可运行")
        print("   3. 无需安装Python环境")
        print("   4. 首次运行可能需要几秒钟启动时间")
        
        # 检查生成的文件
        dist_dir = "dist"
        if os.path.exists(dist_dir):
            exe_files = [f for f in os.listdir(dist_dir) if f.endswith('.exe')]
            if exe_files:
                print(f"\n📊 生成的exe文件:")
                for exe_file in exe_files:
                    file_path = os.path.join(dist_dir, exe_file)
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    print(f"   - {exe_file} ({file_size:.1f} MB)")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 创建exe文件失败")
            print("💡 可能的解决方案:")
            print("   1. 确保网络连接正常")
            print("   2. 以管理员身份运行")
            print("   3. 检查Python环境")
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")
