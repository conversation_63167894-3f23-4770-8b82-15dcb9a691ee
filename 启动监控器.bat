@echo off
chcp 65001 >nul
title BitcoinTalk新帖子监控器

echo.
echo ========================================
echo    BitcoinTalk新帖子监控器 - Windows 10
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装Python
    echo 3. 安装时勾选"Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 🚀 启动BitcoinTalk新帖子监控器...
echo.

REM 启动监控器
python bitcointalk_monitor_win10.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 请检查Python环境或联系技术支持
    pause
)

echo.
echo 程序已退出
pause
