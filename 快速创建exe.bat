@echo off
chcp 65001 >nul
title 快速创建exe文件

echo.
echo ==========================================
echo    BitcoinTalk监控器 - 快速创建exe
echo ==========================================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 📦 安装PyInstaller...
python -m pip install pyinstaller --quiet
if errorlevel 1 (
    echo ❌ PyInstaller安装失败，尝试其他方法...
    python -m pip install --user pyinstaller --quiet
)

echo ✅ PyInstaller准备完成
echo.

echo 🔨 正在创建exe文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 创建exe文件
python -m PyInstaller --onefile --windowed --name="BitcoinTalk监控器" --distpath=. 独立监控器.py

if errorlevel 1 (
    echo ❌ exe创建失败
    echo.
    echo 尝试创建命令行版本...
    python -m PyInstaller --onefile --console --name="BitcoinTalk监控器-命令行" bitcointalk_monitor_simple.py
    
    if errorlevel 1 (
        echo ❌ 命令行版本也创建失败
        echo.
        echo 💡 可能的解决方案:
        echo 1. 以管理员身份运行此脚本
        echo 2. 检查网络连接
        echo 3. 手动安装: python -m pip install pyinstaller
        pause
        exit /b 1
    ) else (
        echo ✅ 命令行版本创建成功！
    )
) else (
    echo ✅ GUI版本创建成功！
)

echo.
echo ========================================
echo 🎉 exe文件创建完成！
echo ========================================
echo.

REM 检查生成的文件
if exist "BitcoinTalk监控器.exe" (
    echo ✅ 找到文件: BitcoinTalk监控器.exe
    for %%I in ("BitcoinTalk监控器.exe") do echo    大小: %%~zI 字节
)

if exist "BitcoinTalk监控器-命令行.exe" (
    echo ✅ 找到文件: BitcoinTalk监控器-命令行.exe
    for %%I in ("BitcoinTalk监控器-命令行.exe") do echo    大小: %%~zI 字节
)

echo.
echo 💡 使用说明:
echo 1. exe文件已生成在当前目录
echo 2. 可以将exe文件复制到任意位置
echo 3. 双击exe文件即可运行
echo 4. 无需安装Python环境
echo 5. 首次运行可能需要几秒钟启动
echo.

REM 清理临时文件
if exist "build" (
    echo 🧹 清理临时文件...
    rmdir /s /q build >nul 2>&1
)

if exist "*.spec" (
    del *.spec >nul 2>&1
)

echo ✅ 清理完成
echo.

REM 测试运行
if exist "BitcoinTalk监控器.exe" (
    set /p test="是否测试运行exe文件? (y/n): "
    if /i "%test%"=="y" (
        echo 🚀 启动exe文件...
        start "" "BitcoinTalk监控器.exe"
    )
)

pause
