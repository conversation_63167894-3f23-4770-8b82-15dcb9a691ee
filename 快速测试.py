#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 检查基本功能
"""

print("开始测试...")

# 测试1: Python版本
import sys
print(f"Python版本: {sys.version}")

# 测试2: 基本模块
try:
    import tkinter
    print("✅ tkinter可用")
except ImportError as e:
    print(f"❌ tkinter不可用: {e}")

try:
    import urllib.request
    print("✅ urllib可用")
except ImportError as e:
    print(f"❌ urllib不可用: {e}")

# 测试3: 网络连接
try:
    import urllib.request
    req = urllib.request.Request('https://www.google.com')
    with urllib.request.urlopen(req, timeout=10) as response:
        print("✅ 网络连接正常")
except Exception as e:
    print(f"❌ 网络连接失败: {e}")

# 测试4: 文件检查
import os
files = ['bitcointalk_monitor_win10.py', 'bitcointalk_monitor_simple.py']
for f in files:
    if os.path.exists(f):
        print(f"✅ {f} 存在")
    else:
        print(f"❌ {f} 不存在")

print("测试完成")
input("按回车键退出...")
