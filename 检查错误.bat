@echo off
chcp 65001 >nul
title 错误检查工具

echo.
echo ========================================
echo    BitcoinTalk监控器 - 错误检查工具
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 解决方案:
    echo 1. 下载Python: https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重启电脑
    goto :error
)

echo ✅ Python环境正常
echo.

echo 🔍 运行快速测试...
python 快速测试.py
if errorlevel 1 (
    echo ❌ 快速测试失败
    goto :error
)

echo.
echo 🔍 测试GUI程序启动...
echo 正在启动GUI程序，请稍候...
timeout /t 3 >nul

python bitcointalk_monitor_win10.py
if errorlevel 1 (
    echo ❌ GUI程序启动失败
    echo.
    echo 可能的原因:
    echo 1. tkinter模块缺失
    echo 2. 显示器/图形驱动问题
    echo 3. 权限问题
    echo.
    echo 建议尝试命令行版本:
    echo python bitcointalk_monitor_simple.py
    goto :error
)

echo ✅ 程序启动成功
goto :end

:error
echo.
echo ❌ 发现问题，请根据上述提示解决
echo.
echo 常见解决方案:
echo 1. 重新安装Python 3.13
echo 2. 确保勾选"Add Python to PATH"
echo 3. 重启电脑
echo 4. 以管理员身份运行
echo 5. 检查防火墙设置
echo.

:end
echo.
pause
