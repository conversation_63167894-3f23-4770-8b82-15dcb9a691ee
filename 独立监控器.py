#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitcoinTalk新帖子监控器 - 独立版本
可以直接运行，无需额外依赖
"""

import urllib.request
import json
import time
import re
import os
import sys
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading

class BitcoinTalkMonitor:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("BitcoinTalk新帖子监控器")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标（使用默认）
        try:
            self.root.iconbitmap(default='')
        except:
            pass
        
        # 数据文件
        self.data_file = "bitcointalk_posts.json"
        self.known_posts = self.load_known_posts()
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.check_interval = 10
        
        self.setup_ui()
        
    def load_known_posts(self):
        """加载已知帖子"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                pass
        return set()
    
    def save_known_posts(self):
        """保存已知帖子"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.known_posts), f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存数据失败: {e}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题区域
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🚀 BitcoinTalk新帖子监控器", 
                              font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(pady=15)
        
        subtitle_label = tk.Label(title_frame, text="专门监控近3天内新发布的帖子", 
                                 font=('Arial', 11), bg='#2c3e50', fg='#ecf0f1')
        subtitle_label.pack()
        
        # 控制面板
        control_frame = tk.Frame(self.root, bg='#ecf0f1', height=60)
        control_frame.pack(fill='x', padx=10, pady=10)
        control_frame.pack_propagate(False)
        
        # 左侧设置
        left_frame = tk.Frame(control_frame, bg='#ecf0f1')
        left_frame.pack(side='left', pady=10)
        
        tk.Label(left_frame, text="检查间隔:", bg='#ecf0f1', font=('Arial', 10)).pack(side='left')
        self.interval_var = tk.StringVar(value="10")
        interval_entry = tk.Entry(left_frame, textvariable=self.interval_var, width=6, font=('Arial', 10))
        interval_entry.pack(side='left', padx=5)
        tk.Label(left_frame, text="分钟", bg='#ecf0f1', font=('Arial', 10)).pack(side='left')
        
        # 右侧按钮
        button_frame = tk.Frame(control_frame, bg='#ecf0f1')
        button_frame.pack(side='right', pady=10)
        
        self.test_btn = tk.Button(button_frame, text="🧪 测试连接", 
                                 command=self.test_connection, bg='#3498db', fg='white',
                                 font=('Arial', 10, 'bold'), padx=15, pady=5)
        self.test_btn.pack(side='left', padx=5)
        
        self.start_btn = tk.Button(button_frame, text="🚀 开始监控", 
                                  command=self.start_monitoring, bg='#27ae60', fg='white',
                                  font=('Arial', 10, 'bold'), padx=15, pady=5)
        self.start_btn.pack(side='left', padx=5)
        
        self.stop_btn = tk.Button(button_frame, text="⏹️ 停止监控", 
                                 command=self.stop_monitoring, bg='#e74c3c', fg='white',
                                 font=('Arial', 10, 'bold'), padx=15, pady=5, state='disabled')
        self.stop_btn.pack(side='left', padx=5)
        
        # 状态栏
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill='x', padx=10, pady=(0, 5))
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="📭 就绪 - 点击'测试连接'开始", 
                                    bg='#34495e', fg='white', anchor='w', font=('Arial', 10))
        self.status_label.pack(fill='x', padx=10, pady=5)
        
        # 日志区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        log_header = tk.Label(log_frame, text="📋 监控日志", font=('Arial', 12, 'bold'), anchor='w')
        log_header.pack(fill='x', pady=(0, 5))
        
        # 创建日志文本框和滚动条
        log_container = tk.Frame(log_frame)
        log_container.pack(fill='both', expand=True)
        
        self.log_text = tk.Text(log_container, font=('Consolas', 9), wrap='word')
        scrollbar = tk.Scrollbar(log_container, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 初始化日志
        self.log("🎯 BitcoinTalk新帖子监控器已启动")
        self.log("💡 提示: 点击'测试连接'验证网络，然后点击'开始监控'")
        self.log("⚠️  注意: 请设置合理的检查间隔（建议10分钟以上）")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def parse_datetime(self, date_str):
        """解析时间"""
        try:
            if "Today" in date_str:
                time_part = date_str.split("at")[-1].strip()
                today = datetime.now().date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(today, time_obj)
            
            if "Yesterday" in date_str:
                time_part = date_str.split("at")[-1].strip()
                yesterday = (datetime.now() - timedelta(days=1)).date()
                time_obj = datetime.strptime(time_part, "%I:%M:%S %p").time()
                return datetime.combine(yesterday, time_obj)
            
            date_match = re.search(r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)', date_str)
            if date_match:
                return datetime.strptime(date_match.group(1), "%B %d, %Y, %I:%M:%S %p")
                
        except Exception:
            pass
        return None
    
    def is_within_3_days(self, post_date):
        """检查是否在近3天内"""
        if not post_date:
            return False
        three_days_ago = datetime.now() - timedelta(days=3)
        return post_date >= three_days_ago
    
    def fetch_new_posts(self):
        """获取新帖子"""
        try:
            self.update_status("🔍 正在获取BitcoinTalk数据...")
            
            url = "https://bitcointalk.org/index.php?board=159.0"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req, timeout=30) as response:
                content = response.read().decode('utf-8', errors='ignore')
            
            self.log(f"✅ 页面获取成功，大小: {len(content):,} 字符")
            
            # 解析新帖子
            new_posts = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                # 查找帖子链接
                topic_match = re.search(r'<a href="https://bitcointalk\.org/index\.php\?topic=(\d+)\.0"[^>]*>([^<]+)</a>', line)
                if not topic_match:
                    continue
                
                topic_id, raw_title = topic_match.groups()
                
                # 清理标题
                title = re.sub(r'<[^>]+>', '', raw_title).strip()
                title = title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                
                # 过滤置顶帖子
                skip_keywords = ['spam', 'rule', 'guideline', 'beware', 'giveaway', 'unofficial list']
                if len(title) < 15 or any(keyword in title.lower() for keyword in skip_keywords):
                    continue
                
                # 查找帖子信息
                post_info = {
                    'topic_id': topic_id,
                    'title': title,
                    'url': f"https://bitcointalk.org/index.php?topic={topic_id}.0"
                }
                
                # 在附近行中查找作者和时间
                search_start = max(0, i - 5)
                search_end = min(len(lines), i + 15)
                
                for j in range(search_start, search_end):
                    search_line = lines[j]
                    
                    # 查找作者
                    if not post_info.get('author'):
                        author_match = re.search(r'<a href="[^"]*action=profile;u=\d+"[^>]*>([^<]+)</a>', search_line)
                        if author_match:
                            post_info['author'] = author_match.group(1).strip()
                    
                    # 查找时间
                    time_patterns = [
                        r'(Today at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(Yesterday at \d{1,2}:\d{2}:\d{2} [AP]M)',
                        r'(\w+ \d{1,2}, \d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
                    ]
                    
                    for pattern in time_patterns:
                        time_match = re.search(pattern, search_line)
                        if time_match:
                            time_str = time_match.group(1)
                            parsed_time = self.parse_datetime(time_str)
                            
                            if parsed_time and self.is_within_3_days(parsed_time):
                                post_info['time_str'] = time_str
                                post_info['parsed_time'] = parsed_time
                                break
                    
                    if post_info.get('time_str'):
                        break
                
                # 检查是否是新帖子
                if (post_info.get('parsed_time') and 
                    self.is_within_3_days(post_info['parsed_time']) and
                    topic_id not in self.known_posts):
                    
                    new_posts.append({
                        'topic_id': topic_id,
                        'title': title,
                        'author': post_info.get('author', '未知'),
                        'time_str': post_info.get('time_str', ''),
                        'url': post_info['url'],
                        'found_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
                    # 添加到已知帖子
                    self.known_posts.add(topic_id)
            
            return new_posts
            
        except Exception as e:
            self.log(f"❌ 获取数据失败: {e}")
            self.update_status("❌ 获取数据失败")
            return []
    
    def display_new_posts(self, posts):
        """显示新帖子"""
        if not posts:
            self.log("📭 没有发现新发布的帖子")
            self.update_status("📭 没有新帖子")
            return
        
        self.log(f"🎉 发现 {len(posts)} 个新发布的帖子:")
        self.log("=" * 70)
        
        for i, post in enumerate(posts, 1):
            self.log(f"{i}. 🆕 {post['title']}")
            self.log(f"   👤 作者: {post['author']}")
            self.log(f"   🕒 发布时间: {post['time_str']}")
            self.log(f"   🔗 链接: {post['url']}")
            self.log(f"   📅 发现时间: {post['found_at']}")
            self.log("-" * 60)
        
        self.update_status(f"🎉 发现 {len(posts)} 个新帖子")
    
    def test_connection(self):
        """测试连接"""
        self.log("🧪 开始测试连接...")
        try:
            posts = self.fetch_new_posts()
            total_recent = len([p for p in posts if p]) + len(self.known_posts)
            self.log(f"✅ 连接测试成功！找到 {total_recent} 个近期帖子")
            messagebox.showinfo("测试成功", f"连接正常！\n找到 {total_recent} 个近期帖子\n\n可以开始监控了。")
        except Exception as e:
            self.log(f"❌ 连接测试失败: {e}")
            messagebox.showerror("测试失败", f"连接失败:\n{e}\n\n请检查网络连接。")
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self.log(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 检查新帖子...")
                new_posts = self.fetch_new_posts()
                self.display_new_posts(new_posts)
                
                if new_posts:
                    self.save_known_posts()
                
                # 等待指定间隔
                for i in range(self.check_interval * 60):
                    if not self.monitoring:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.log(f"❌ 监控过程中出错: {e}")
                time.sleep(60)
    
    def start_monitoring(self):
        """开始监控"""
        try:
            self.check_interval = int(self.interval_var.get())
            if self.check_interval < 5:
                raise ValueError("间隔时间不能少于5分钟")
        except ValueError as e:
            messagebox.showerror("设置错误", f"检查间隔设置无效: {e}")
            return
        
        self.monitoring = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.test_btn.config(state='disabled')
        
        self.log(f"🚀 开始监控，检查间隔: {self.check_interval} 分钟")
        self.update_status(f"🔄 监控中 (每{self.check_interval}分钟检查)")
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.test_btn.config(state='normal')
        
        self.log("⏹️ 监控已停止")
        self.update_status("📭 监控已停止")
    
    def on_closing(self):
        """关闭程序"""
        if self.monitoring:
            if messagebox.askokcancel("退出", "监控正在运行，确定要退出吗？"):
                self.stop_monitoring()
                time.sleep(1)
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 居中显示窗口
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = BitcoinTalkMonitor()
        app.run()
    except Exception as e:
        # 如果GUI失败，显示错误信息
        try:
            import tkinter.messagebox as mb
            mb.showerror("启动失败", f"程序启动失败:\n{e}\n\n请检查Python环境。")
        except:
            print(f"程序启动失败: {e}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
