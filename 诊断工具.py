#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitcoinTalk监控器诊断工具
检查系统环境和可能的错误
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ Python版本符合要求")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7或更高版本")
        return False

def check_modules():
    """检查必需的模块"""
    print("\n🔍 检查必需模块...")
    
    modules = {
        'urllib.request': '网络请求',
        'tkinter': 'GUI界面',
        'json': 'JSON处理',
        'datetime': '时间处理',
        'threading': '多线程',
        're': '正则表达式'
    }
    
    missing_modules = []
    
    for module, description in modules.items():
        try:
            __import__(module)
            print(f"   ✅ {module} ({description}) - 可用")
        except ImportError as e:
            print(f"   ❌ {module} ({description}) - 缺失: {e}")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def check_tkinter():
    """专门检查tkinter"""
    print("\n🔍 检查GUI支持...")
    try:
        import tkinter as tk
        # 尝试创建一个测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("   ✅ tkinter GUI支持正常")
        return True
    except Exception as e:
        print(f"   ❌ tkinter GUI支持有问题: {e}")
        print("   💡 可能需要安装tkinter: pip install tk")
        return False

def check_network():
    """检查网络连接"""
    print("\n🔍 检查网络连接...")
    try:
        import urllib.request
        
        # 测试基本网络
        print("   测试网络连接...")
        req = urllib.request.Request('https://www.google.com', 
                                   headers={'User-Agent': 'Mozilla/5.0'})
        with urllib.request.urlopen(req, timeout=10) as response:
            if response.status == 200:
                print("   ✅ 基本网络连接正常")
            else:
                print(f"   ⚠️ 网络响应异常: {response.status}")
        
        # 测试BitcoinTalk连接
        print("   测试BitcoinTalk连接...")
        req = urllib.request.Request('https://bitcointalk.org', 
                                   headers={'User-Agent': 'Mozilla/5.0'})
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.status == 200:
                print("   ✅ BitcoinTalk连接正常")
                return True
            else:
                print(f"   ⚠️ BitcoinTalk响应异常: {response.status}")
                return False
                
    except Exception as e:
        print(f"   ❌ 网络连接失败: {e}")
        return False

def check_files():
    """检查程序文件"""
    print("\n🔍 检查程序文件...")
    
    files = [
        'bitcointalk_monitor_win10.py',
        'bitcointalk_monitor_simple.py',
        '启动监控器.bat',
        '简单启动.bat'
    ]
    
    all_exist = True
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} - 存在 ({size} 字节)")
        else:
            print(f"   ❌ {file} - 缺失")
            all_exist = False
    
    return all_exist

def test_simple_monitor():
    """测试简单监控功能"""
    print("\n🔍 测试基本监控功能...")
    try:
        import urllib.request
        import re
        from datetime import datetime
        
        print("   测试网页获取...")
        url = "https://bitcointalk.org/index.php?board=159.0"
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        
        req = urllib.request.Request(url, headers=headers)
        with urllib.request.urlopen(req, timeout=30) as response:
            content = response.read().decode('utf-8', errors='ignore')
        
        print(f"   ✅ 页面获取成功，大小: {len(content):,} 字符")
        
        # 测试解析
        print("   测试内容解析...")
        topic_matches = re.findall(r'topic=(\d+)', content)
        print(f"   ✅ 找到 {len(set(topic_matches))} 个帖子ID")
        
        time_matches = re.findall(r'(Today|Yesterday|\w+ \d+, 2025)', content)
        print(f"   ✅ 找到 {len(time_matches)} 个时间标记")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        return False

def run_gui_test():
    """运行GUI测试"""
    print("\n🔍 测试GUI启动...")
    try:
        import tkinter as tk
        from tkinter import ttk
        
        print("   创建测试窗口...")
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        label = tk.Label(root, text="✅ GUI测试成功！\n如果您看到这个窗口，\n说明GUI功能正常。")
        label.pack(expand=True)
        
        def close_test():
            print("   ✅ GUI测试完成")
            root.destroy()
        
        button = tk.Button(root, text="关闭测试", command=close_test)
        button.pack(pady=10)
        
        print("   ✅ 测试窗口已创建")
        print("   💡 请查看是否弹出了测试窗口")
        
        # 3秒后自动关闭
        root.after(3000, close_test)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"   ❌ GUI测试失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🚀 BitcoinTalk监控器诊断工具")
    print("=" * 50)
    
    # 检查各项
    python_ok = check_python_version()
    modules_ok = check_modules()
    tkinter_ok = check_tkinter()
    network_ok = check_network()
    files_ok = check_files()
    monitor_ok = test_simple_monitor()
    
    print("\n" + "=" * 50)
    print("📊 诊断结果汇总:")
    print("=" * 50)
    
    results = [
        ("Python版本", python_ok),
        ("必需模块", modules_ok),
        ("GUI支持", tkinter_ok),
        ("网络连接", network_ok),
        ("程序文件", files_ok),
        ("监控功能", monitor_ok)
    ]
    
    all_ok = True
    for name, status in results:
        icon = "✅" if status else "❌"
        print(f"{icon} {name}: {'正常' if status else '有问题'}")
        if not status:
            all_ok = False
    
    print("\n" + "=" * 50)
    
    if all_ok:
        print("🎉 所有检查都通过了！")
        print("💡 建议:")
        print("   1. 尝试运行: python bitcointalk_monitor_simple.py")
        print("   2. 或双击: 简单启动.bat")
        print("   3. 如果还有问题，可能是防火墙或权限问题")
        
        # 运行GUI测试
        if input("\n是否运行GUI测试? (y/n): ").lower().startswith('y'):
            run_gui_test()
            
    else:
        print("❌ 发现问题，请根据上述检查结果解决:")
        print("\n🔧 常见解决方案:")
        
        if not python_ok:
            print("   - 升级Python到3.7+版本")
        
        if not modules_ok:
            print("   - 重新安装Python，确保包含所有标准库")
        
        if not tkinter_ok:
            print("   - Windows: 重新安装Python，勾选tcl/tk选项")
            print("   - 或使用命令行版本: python bitcointalk_monitor_simple.py")
        
        if not network_ok:
            print("   - 检查网络连接和防火墙设置")
            print("   - 尝试使用VPN")
        
        if not files_ok:
            print("   - 确保所有程序文件都在同一目录")
        
        if not monitor_ok:
            print("   - 检查网络连接")
            print("   - 确保能访问bitcointalk.org")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 诊断已取消")
    except Exception as e:
        print(f"\n❌ 诊断工具出错: {e}")
        print("请手动检查Python环境和网络连接")
    
    input("\n按回车键退出...")
